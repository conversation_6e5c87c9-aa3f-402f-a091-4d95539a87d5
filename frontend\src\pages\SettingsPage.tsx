import { useState, useEffect } from 'react'
import {
  Box,
  Container,
  Heading,
  Text,
  VStack,
  HStack,
  Button,
  Card,
  CardBody,
  CardHeader,
  FormControl,
  FormLabel,
  Input,
  Select,
  Switch,
  Divider,
  useToast,
  Alert,
  AlertIcon,
  Tabs,
  TabList,
  TabPanels,
  Tab,
  TabPanel,
  Badge,
  InputGroup,
  InputRightElement,
  IconButton,
} from '@chakra-ui/react'
import { FaEye, FaEyeSlash, FaArrowLeft } from 'react-icons/fa'
import { useNavigate } from 'react-router-dom'
import { useAuth } from '../contexts/AuthContext'
import { settingsAPI } from '../services/api'

interface UserSettings {
  id: number
  user_id: number
  default_llm_provider: string
  openai_api_key?: string
  anthropic_api_key?: string
  google_api_key?: string
  ollama_endpoint?: string
  default_model: string
  theme: string
  primary_color: string
  font_size: string
  animations_enabled: boolean
  default_research_depth: string
  auto_export_format: string
  created_at: string
  updated_at: string
}

const SettingsPage = () => {
  const [settings, setSettings] = useState<UserSettings | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [isSaving, setIsSaving] = useState(false)
  const [showApiKeys, setShowApiKeys] = useState<Record<string, boolean>>({})
  const [availableProviders, setAvailableProviders] = useState<any>(null)
  const [availableModels, setAvailableModels] = useState<any>(null)
  const [isLoadingModels, setIsLoadingModels] = useState(false)

  const { user, token } = useAuth()
  const navigate = useNavigate()
  const toast = useToast()

  useEffect(() => {
    loadSettings()
    loadAvailableProviders()
    loadAvailableModels()
  }, [])

  // Load models when provider changes
  useEffect(() => {
    if (settings?.default_llm_provider === 'google') {
      loadAvailableModels()
    }
  }, [settings?.default_llm_provider])

  const loadSettings = async () => {
    if (!token) return

    try {
      const userSettings = await settingsAPI.getUserSettings(token)
      setSettings(userSettings)
    } catch (error: any) {
      toast({
        title: 'Error loading settings',
        description: error.message,
        status: 'error',
        duration: 5000,
        isClosable: true,
      })
    } finally {
      setIsLoading(false)
    }
  }

  const loadAvailableProviders = async () => {
    try {
      const providers = await settingsAPI.getAvailableLLMProviders()
      setAvailableProviders(providers)
    } catch (error) {
      console.error('Failed to load providers:', error)
    }
  }

  const loadAvailableModels = async () => {
    setIsLoadingModels(true)
    try {
      const models = await settingsAPI.getAvailableModels()
      setAvailableModels(models)
    } catch (error) {
      console.error('Failed to load models:', error)
      toast({
        title: 'Error loading models',
        description: 'Failed to fetch available models from API',
        status: 'warning',
        duration: 3000,
        isClosable: true,
      })
    } finally {
      setIsLoadingModels(false)
    }
  }

  const handleSaveSettings = async () => {
    if (!token || !settings) return

    setIsSaving(true)
    try {
      const updatedSettings = await settingsAPI.updateUserSettings(token, settings)
      setSettings(updatedSettings)
      toast({
        title: 'Settings saved',
        description: 'Your settings have been updated successfully.',
        status: 'success',
        duration: 3000,
        isClosable: true,
      })
    } catch (error: any) {
      toast({
        title: 'Error saving settings',
        description: error.message,
        status: 'error',
        duration: 5000,
        isClosable: true,
      })
    } finally {
      setIsSaving(false)
    }
  }

  const handleResetSettings = async () => {
    if (!token) return

    try {
      await settingsAPI.resetUserSettings(token)
      await loadSettings()
      toast({
        title: 'Settings reset',
        description: 'Your settings have been reset to default values.',
        status: 'info',
        duration: 3000,
        isClosable: true,
      })
    } catch (error: any) {
      toast({
        title: 'Error resetting settings',
        description: error.message,
        status: 'error',
        duration: 5000,
        isClosable: true,
      })
    }
  }

  const updateSetting = (field: keyof UserSettings, value: any) => {
    if (!settings) return
    setSettings({ ...settings, [field]: value })
  }

  const toggleApiKeyVisibility = (provider: string) => {
    setShowApiKeys(prev => ({ ...prev, [provider]: !prev[provider] }))
  }

  if (isLoading) {
    return (
      <Box minH="100vh" bg="gray.900" p={6}>
        <Container maxW="4xl">
          <Text color="gray.400">Loading settings...</Text>
        </Container>
      </Box>
    )
  }

  if (!settings) {
    return (
      <Box minH="100vh" bg="gray.900" p={6}>
        <Container maxW="4xl">
          <Alert status="error">
            <AlertIcon />
            Failed to load settings
          </Alert>
        </Container>
      </Box>
    )
  }

  return (
    <Box minH="100vh" bg="gray.900" p={6}>
      <Container maxW="4xl">
        <VStack spacing={6} align="stretch">
          {/* Header */}
          <HStack justify="space-between">
            <HStack spacing={4}>
              <IconButton
                aria-label="Back to dashboard"
                icon={<FaArrowLeft />}
                variant="ghost"
                color="gray.400"
                _hover={{ color: 'gray.300', bg: 'gray.700' }}
                onClick={() => navigate('/dashboard')}
              />
              <VStack align="start" spacing={1}>
                <Heading size="xl" color="gray.100">
                  Settings
                </Heading>
                <Text color="gray.400">
                  Configure your Expendra experience
                </Text>
              </VStack>
            </HStack>
            <HStack spacing={3}>
              <Button
                variant="outline"
                colorScheme="red"
                onClick={handleResetSettings}
                size="sm"
              >
                Reset to Defaults
              </Button>
              <Button
                colorScheme="brand"
                onClick={handleSaveSettings}
                isLoading={isSaving}
                loadingText="Saving..."
              >
                Save Changes
              </Button>
            </HStack>
          </HStack>

          {/* Settings Tabs */}
          <Tabs colorScheme="brand" variant="enclosed">
            <TabList bg="gray.800" borderColor="gray.700">
              <Tab color="gray.400" _selected={{ color: 'white', bg: 'gray.700' }}>
                AI Providers
              </Tab>
              <Tab color="gray.400" _selected={{ color: 'white', bg: 'gray.700' }}>
                Appearance
              </Tab>
              <Tab color="gray.400" _selected={{ color: 'white', bg: 'gray.700' }}>
                Research
              </Tab>
            </TabList>

            <TabPanels>
              {/* AI Providers Tab */}
              <TabPanel px={0}>
                <VStack spacing={6} align="stretch">
                  <Card bg="gray.800" borderColor="gray.700">
                    <CardHeader>
                      <Heading size="md" color="gray.100">
                        LLM Provider Configuration
                      </Heading>
                      <Text color="gray.400" fontSize="sm" mt={2}>
                        Configure your preferred AI providers and API keys
                      </Text>
                    </CardHeader>
                    <CardBody>
                      <VStack spacing={6}>
                        <FormControl>
                          <FormLabel color="gray.300">Default Provider</FormLabel>
                          <Select
                            value={settings.default_llm_provider}
                            onChange={(e) => updateSetting('default_llm_provider', e.target.value)}
                            bg="gray.700"
                            borderColor="gray.600"
                            color="gray.100"
                          >
                            <option value="openai">OpenAI</option>
                            <option value="anthropic">Anthropic (Claude)</option>
                            <option value="google">Google (Gemini)</option>
                            <option value="ollama">Ollama (Local)</option>
                          </Select>
                        </FormControl>

                        <FormControl>
                          <FormLabel color="gray.300">
                            Default Model
                            {isLoadingModels && (
                              <Badge ml={2} colorScheme="blue" variant="subtle">
                                Loading...
                              </Badge>
                            )}
                          </FormLabel>
                          {settings.default_llm_provider === 'google' && availableModels?.models ? (
                            <Select
                              value={settings.default_model}
                              onChange={(e) => updateSetting('default_model', e.target.value)}
                              bg="gray.700"
                              borderColor="gray.600"
                              color="gray.100"
                              placeholder="Select a model..."
                            >
                              {Object.entries(availableModels.models).map(([modelId, modelInfo]: [string, any]) => (
                                <option key={modelId} value={modelId}>
                                  {modelInfo.name} ({modelId})
                                </option>
                              ))}
                            </Select>
                          ) : (
                            <Input
                              value={settings.default_model}
                              onChange={(e) => updateSetting('default_model', e.target.value)}
                              placeholder="e.g., gpt-4, claude-3-sonnet, gemini-2.0-flash-001"
                              bg="gray.700"
                              borderColor="gray.600"
                              color="gray.100"
                            />
                          )}
                          {settings.default_llm_provider === 'google' && availableModels?.status === 'error' && (
                            <Text color="orange.300" fontSize="sm" mt={1}>
                              ⚠️ Could not load models from API. Using manual input.
                            </Text>
                          )}
                        </FormControl>

                        <Divider borderColor="gray.600" />

                        {/* API Keys Section */}
                        <VStack spacing={4} align="stretch" w="full">
                          <Text color="gray.300" fontWeight="semibold">
                            API Keys
                          </Text>

                          {/* OpenAI API Key */}
                          <FormControl>
                            <FormLabel color="gray.300">
                              OpenAI API Key
                              {settings.openai_api_key && (
                                <Badge ml={2} colorScheme="green" size="sm">
                                  Configured
                                </Badge>
                              )}
                            </FormLabel>
                            <InputGroup>
                              <Input
                                type={showApiKeys.openai ? 'text' : 'password'}
                                value={settings.openai_api_key || ''}
                                onChange={(e) => updateSetting('openai_api_key', e.target.value)}
                                placeholder="sk-..."
                                bg="gray.700"
                                borderColor="gray.600"
                                color="gray.100"
                              />
                              <InputRightElement>
                                <IconButton
                                  aria-label="Toggle API key visibility"
                                  icon={showApiKeys.openai ? <FaEyeSlash /> : <FaEye />}
                                  onClick={() => toggleApiKeyVisibility('openai')}
                                  variant="ghost"
                                  size="sm"
                                  color="gray.400"
                                />
                              </InputRightElement>
                            </InputGroup>
                          </FormControl>

                          {/* Anthropic API Key */}
                          <FormControl>
                            <FormLabel color="gray.300">
                              Anthropic API Key
                              {settings.anthropic_api_key && (
                                <Badge ml={2} colorScheme="green" size="sm">
                                  Configured
                                </Badge>
                              )}
                            </FormLabel>
                            <InputGroup>
                              <Input
                                type={showApiKeys.anthropic ? 'text' : 'password'}
                                value={settings.anthropic_api_key || ''}
                                onChange={(e) => updateSetting('anthropic_api_key', e.target.value)}
                                placeholder="sk-ant-..."
                                bg="gray.700"
                                borderColor="gray.600"
                                color="gray.100"
                              />
                              <InputRightElement>
                                <IconButton
                                  aria-label="Toggle API key visibility"
                                  icon={showApiKeys.anthropic ? <FaEyeSlash /> : <FaEye />}
                                  onClick={() => toggleApiKeyVisibility('anthropic')}
                                  variant="ghost"
                                  size="sm"
                                  color="gray.400"
                                />
                              </InputRightElement>
                            </InputGroup>
                          </FormControl>

                          {/* Google API Key */}
                          <FormControl>
                            <FormLabel color="gray.300">
                              Google API Key
                              {settings.google_api_key && (
                                <Badge ml={2} colorScheme="green" size="sm">
                                  Configured
                                </Badge>
                              )}
                            </FormLabel>
                            <InputGroup>
                              <Input
                                type={showApiKeys.google ? 'text' : 'password'}
                                value={settings.google_api_key || ''}
                                onChange={(e) => updateSetting('google_api_key', e.target.value)}
                                placeholder="AIza..."
                                bg="gray.700"
                                borderColor="gray.600"
                                color="gray.100"
                              />
                              <InputRightElement>
                                <IconButton
                                  aria-label="Toggle API key visibility"
                                  icon={showApiKeys.google ? <FaEyeSlash /> : <FaEye />}
                                  onClick={() => toggleApiKeyVisibility('google')}
                                  variant="ghost"
                                  size="sm"
                                  color="gray.400"
                                />
                              </InputRightElement>
                            </InputGroup>
                          </FormControl>

                          {/* Ollama Endpoint */}
                          <FormControl>
                            <FormLabel color="gray.300">
                              Ollama Endpoint
                              {settings.ollama_endpoint && (
                                <Badge ml={2} colorScheme="green" size="sm">
                                  Configured
                                </Badge>
                              )}
                            </FormLabel>
                            <Input
                              value={settings.ollama_endpoint || ''}
                              onChange={(e) => updateSetting('ollama_endpoint', e.target.value)}
                              placeholder="http://localhost:11434"
                              bg="gray.700"
                              borderColor="gray.600"
                              color="gray.100"
                            />
                          </FormControl>
                        </VStack>
                      </VStack>
                    </CardBody>
                  </Card>
                </VStack>
              </TabPanel>

              {/* Appearance Tab */}
              <TabPanel px={0}>
                <VStack spacing={6} align="stretch">
                  <Card bg="gray.800" borderColor="gray.700">
                    <CardHeader>
                      <Heading size="md" color="gray.100">
                        Appearance Settings
                      </Heading>
                      <Text color="gray.400" fontSize="sm" mt={2}>
                        Customize the look and feel of your interface
                      </Text>
                    </CardHeader>
                    <CardBody>
                      <VStack spacing={6}>
                        <FormControl>
                          <FormLabel color="gray.300">Theme</FormLabel>
                          <Select
                            value={settings.theme}
                            onChange={(e) => updateSetting('theme', e.target.value)}
                            bg="gray.700"
                            borderColor="gray.600"
                            color="gray.100"
                          >
                            <option value="dark">Dark</option>
                            <option value="light">Light</option>
                            <option value="auto">Auto (System)</option>
                          </Select>
                        </FormControl>

                        <FormControl>
                          <FormLabel color="gray.300">Primary Color</FormLabel>
                          <Select
                            value={settings.primary_color}
                            onChange={(e) => updateSetting('primary_color', e.target.value)}
                            bg="gray.700"
                            borderColor="gray.600"
                            color="gray.100"
                          >
                            <option value="blue">Blue</option>
                            <option value="purple">Purple</option>
                            <option value="green">Green</option>
                            <option value="orange">Orange</option>
                            <option value="red">Red</option>
                          </Select>
                        </FormControl>

                        <FormControl>
                          <FormLabel color="gray.300">Font Size</FormLabel>
                          <Select
                            value={settings.font_size}
                            onChange={(e) => updateSetting('font_size', e.target.value)}
                            bg="gray.700"
                            borderColor="gray.600"
                            color="gray.100"
                          >
                            <option value="small">Small</option>
                            <option value="medium">Medium</option>
                            <option value="large">Large</option>
                          </Select>
                        </FormControl>

                        <FormControl display="flex" alignItems="center">
                          <FormLabel color="gray.300" mb="0">
                            Enable Animations
                          </FormLabel>
                          <Switch
                            isChecked={settings.animations_enabled}
                            onChange={(e) => updateSetting('animations_enabled', e.target.checked)}
                            colorScheme="brand"
                          />
                        </FormControl>
                      </VStack>
                    </CardBody>
                  </Card>
                </VStack>
              </TabPanel>

              {/* Research Tab */}
              <TabPanel px={0}>
                <VStack spacing={6} align="stretch">
                  <Card bg="gray.800" borderColor="gray.700">
                    <CardHeader>
                      <Heading size="md" color="gray.100">
                        Research Preferences
                      </Heading>
                      <Text color="gray.400" fontSize="sm" mt={2}>
                        Configure default settings for research and exports
                      </Text>
                    </CardHeader>
                    <CardBody>
                      <VStack spacing={6}>
                        <FormControl>
                          <FormLabel color="gray.300">Default Research Depth</FormLabel>
                          <Select
                            value={settings.default_research_depth}
                            onChange={(e) => updateSetting('default_research_depth', e.target.value)}
                            bg="gray.700"
                            borderColor="gray.600"
                            color="gray.100"
                          >
                            <option value="quick">Quick (5-10 sources)</option>
                            <option value="standard">Standard (10-20 sources)</option>
                            <option value="comprehensive">Comprehensive (20+ sources)</option>
                          </Select>
                        </FormControl>

                        <FormControl>
                          <FormLabel color="gray.300">Auto Export Format</FormLabel>
                          <Select
                            value={settings.auto_export_format}
                            onChange={(e) => updateSetting('auto_export_format', e.target.value)}
                            bg="gray.700"
                            borderColor="gray.600"
                            color="gray.100"
                          >
                            <option value="pdf">PDF</option>
                            <option value="excel">Excel</option>
                            <option value="csv">CSV</option>
                            <option value="json">JSON</option>
                          </Select>
                        </FormControl>
                      </VStack>
                    </CardBody>
                  </Card>
                </VStack>
              </TabPanel>
            </TabPanels>
          </Tabs>
        </VStack>
      </Container>
    </Box>
  )
}

export default SettingsPage
